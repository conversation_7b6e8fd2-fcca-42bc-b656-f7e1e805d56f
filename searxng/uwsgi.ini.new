[uwsgi]
# Listening address
# default value: [::]:8080 (see Dockerfile)
http-socket = $(BIND_ADDRESS)

# Who will run the code
uid = searxng
gid = searxng

# Number of workers (usually CPU count)
# default value: %k (= number of CPU core, see Dockerfile)
workers = $(UWSGI_WORKERS)

# Number of threads per worker
# default value: 4 (see Dockerfile)
threads = $(UWSGI_THREADS)

# The right granted on the created socket
chmod-socket = 666

# Plugin to use and interpreter config
single-interpreter = true
master = true
lazy-apps = true
enable-threads = true

# Module to import
module = searx.webapp

# Virtualenv and python path
pythonpath = /usr/local/searxng/
chdir = /usr/local/searxng/searx/

# automatically set processes name to something meaningful
auto-procname = true

# Disable request logging for privacy
disable-logging = true
log-5xx = true

# Set the max size of a request (request-body excluded)
buffer-size = 8192

# No keep alive
# See https://github.com/searx/searx-docker/issues/24
add-header = Connection: close

# Follow SIGTERM convention
# See https://github.com/searxng/searxng/issues/3427
die-on-term

# uwsgi serves the static files
static-map = /static=/usr/local/searxng/searx/static
static-gzip-all = True
offload-threads = %k
